# Custom RBAC (Role-Based Access Control) System

This Django project implements a custom RBAC system with PostgreSQL database connection, replacing Django's built-in authentication system.

## Database Configuration

- **Database**: PostgreSQL
- **Host**: localhost
- **Database Name**: erp
- **User**: postgres
- **Password**: 1234
- **Port**: 5432

## RBAC Models

### 1. User
Custom user model with the following fields:
- `username` (unique)
- `email` (unique)
- `password` (hashed)
- `first_name`, `last_name`
- `is_active`, `is_staff`, `is_superuser`
- `date_joined`, `last_login`

### 2. Role
Defines roles in the system:
- `name` (unique)
- `description`
- `is_active`
- `created_at`, `updated_at`

### 3. Permission
Defines specific permissions:
- `name`
- `codename` (unique)
- `content_type` (optional)
- `description`
- `created_at`

### 4. UserRole
Many-to-many relationship between User and Role:
- `user` (FK to User)
- `role` (FK to Role)
- `assigned_at`
- `assigned_by` (FK to User)
- `is_active`

### 5. RolePermission
Many-to-many relationship between Role and Permission:
- `role` (FK to Role)
- `permission` (FK to Permission)
- `granted_at`
- `granted_by` (FK to User)

## Default Roles and Permissions

### Roles Created by Setup:
1. **Administrator**: Full system access with all permissions
2. **Manager**: Can manage users and view most data
3. **User**: Basic user with limited permissions
4. **Viewer**: Read-only access to most data

### Default Permissions:
- `view_user`, `add_user`, `change_user`, `delete_user`
- `view_role`, `add_role`, `change_role`, `delete_role`
- `view_permission`
- `manage_user_roles`, `manage_role_permissions`

## Management Commands

### 1. Setup RBAC System
```bash
python manage.py setup_rbac
```
Creates default roles and permissions.

### 2. Create Superuser
```bash
python manage.py create_superuser --username admin --email <EMAIL> --password admin123
```

### 3. Manage RBAC
```bash
# List all users
python manage.py manage_rbac list-users

# List all roles
python manage.py manage_rbac list-roles

# Assign role to user
python manage.py manage_rbac assign-role --username testuser --role Manager

# Remove role from user
python manage.py manage_rbac remove-role --username testuser --role Manager

# Show user permissions
python manage.py manage_rbac user-permissions --username testuser
```

## Testing the System

Run the test script to verify everything is working:
```bash
python test_rbac.py
```

## Usage Examples

### Creating a User
```python
from authentication.models import User

user = User.objects.create(
    username='newuser',
    email='<EMAIL>',
    first_name='New',
    last_name='User',
    is_active=True
)
user.set_password('password123')
user.save()
```

### Assigning a Role
```python
from authentication.models import User, Role, UserRole

user = User.objects.get(username='newuser')
role = Role.objects.get(name='Manager')

UserRole.objects.create(
    user=user,
    role=role,
    is_active=True
)
```

### Checking Permissions
```python
user = User.objects.get(username='newuser')

# Check specific permission
if user.has_perm('view_user'):
    print("User can view users")

# Check multiple permissions
if user.has_perms(['view_user', 'add_user']):
    print("User can view and add users")
```

### Authentication
```python
from authentication.backends import CustomAuthBackend

backend = CustomAuthBackend()
user = backend.authenticate(None, username='newuser', password='password123')

if user:
    print(f"Authentication successful for {user.username}")
else:
    print("Authentication failed")
```

## Key Features

1. **Custom User Model**: Replaces Django's built-in User model
2. **Flexible RBAC**: Users can have multiple roles, roles can have multiple permissions
3. **PostgreSQL Integration**: Uses PostgreSQL database with specified credentials
4. **Management Commands**: Easy-to-use commands for system management
5. **Permission Checking**: Built-in methods for checking user permissions
6. **Audit Trail**: Tracks who assigned roles and permissions
7. **Active/Inactive States**: Roles and user-role assignments can be deactivated

## Database Schema

The system creates the following tables:
- `auth_user` - Custom user table
- `auth_role` - Roles table
- `auth_permission` - Permissions table
- `auth_user_role` - User-Role relationships
- `auth_role_permission` - Role-Permission relationships

## Security Notes

- Passwords are hashed using Django's built-in password hashers
- Superusers have all permissions by default
- Inactive users cannot authenticate
- Role assignments can be tracked and audited
