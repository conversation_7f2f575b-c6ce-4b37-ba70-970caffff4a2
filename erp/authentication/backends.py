from .models import User


class CustomAuthBackend:
    """
    Custom authentication backend for our RBAC system
    """

    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        Authenticate a user with username and password
        """
        if username is None or password is None:
            return None

        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            return None

        if user.check_password(password) and user.is_active:
            return user
        return None

    def get_user(self, user_id):
        """
        Get user by ID
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None

    def user_can_authenticate(self, user):
        """
        Check if user is allowed to authenticate
        """
        return getattr(user, 'is_active', None)

    def has_perm(self, user_obj, perm, obj=None):
        """
        Check if user has a specific permission
        """
        if not user_obj.is_active:
            return False
        return user_obj.has_perm(perm, obj)

    def has_module_perms(self, user_obj, app_label):
        """
        Check if user has permissions for a specific app
        """
        if not user_obj.is_active:
            return False
        return user_obj.has_module_perms(app_label)

    def get_all_permissions(self, user_obj, obj=None):
        """
        Get all permissions for a user
        """
        if not user_obj.is_active or user_obj.is_anonymous:
            return set()

        if user_obj.is_superuser:
            from .models import Permission
            return set(Permission.objects.values_list('codename', flat=True))

        from .models import Permission
        return set(
            Permission.objects.filter(
                rolepermission__role__userrole__user=user_obj,
                rolepermission__role__userrole__is_active=True
            ).values_list('codename', flat=True)
        )

    def get_user_permissions(self, user_obj, obj=None):
        """
        Get permissions directly assigned to user (through roles)
        """
        return self.get_all_permissions(user_obj, obj)

    def get_group_permissions(self, user_obj, obj=None):
        """
        Get permissions from groups (roles in our case)
        """
        return self.get_all_permissions(user_obj, obj)
