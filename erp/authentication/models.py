from django.db import models
from django.contrib.auth.hashers import make_password, check_password
from django.utils import timezone


class User(models.Model):
    """Custom User model for RBAC system"""
    username = models.Char<PERSON>ield(max_length=150, unique=True)
    email = models.Email<PERSON>ield(unique=True)
    password = models.Char<PERSON>ield(max_length=128)
    first_name = models.Char<PERSON>ield(max_length=30, blank=True)
    last_name = models.Char<PERSON>ield(max_length=30, blank=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)
    date_joined = models.DateTimeField(default=timezone.now)
    last_login = models.DateTimeField(null=True, blank=True)

    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = ['email']

    class Meta:
        db_table = 'auth_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return self.username

    def set_password(self, raw_password):
        """Hash and set the password"""
        self.password = make_password(raw_password)

    def check_password(self, raw_password):
        """Check if the provided password matches the stored password"""
        return check_password(raw_password, self.password)

    def get_full_name(self):
        """Return the first_name plus the last_name, with a space in between"""
        full_name = f'{self.first_name} {self.last_name}'
        return full_name.strip()

    def get_short_name(self):
        """Return the short name for the user"""
        return self.first_name

    def has_perm(self, perm, obj=None):
        """Check if user has a specific permission"""
        if self.is_superuser:
            return True

        # Get all permissions through roles
        user_permissions = Permission.objects.filter(
            rolepermission__role__userrole__user=self
        ).values_list('codename', flat=True)

        return perm in user_permissions

    def has_perms(self, perm_list, obj=None):
        """Check if user has all permissions in the list"""
        return all(self.has_perm(perm, obj) for perm in perm_list)

    def has_module_perms(self, app_label):
        """Check if user has any permissions in the given app_label"""
        if self.is_superuser:
            return True

        user_permissions = Permission.objects.filter(
            rolepermission__role__userrole__user=self,
            content_type__app_label=app_label
        )

        return user_permissions.exists()


class Role(models.Model):
    """Role model for RBAC system"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'auth_role'
        verbose_name = 'Role'
        verbose_name_plural = 'Roles'
        ordering = ['name']

    def __str__(self):
        return self.name


class Permission(models.Model):
    """Permission model for RBAC system"""
    name = models.CharField(max_length=100)
    codename = models.CharField(max_length=100, unique=True)
    content_type = models.ForeignKey(
        'contenttypes.ContentType',
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'auth_permission'
        verbose_name = 'Permission'
        verbose_name_plural = 'Permissions'
        unique_together = [['content_type', 'codename']]
        ordering = ['content_type__app_label', 'codename']

    def __str__(self):
        return f"{self.content_type.app_label if self.content_type else 'global'}.{self.codename}"


class UserRole(models.Model):
    """Many-to-many relationship between User and Role"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_roles'
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'auth_user_role'
        verbose_name = 'User Role'
        verbose_name_plural = 'User Roles'
        unique_together = [['user', 'role']]

    def __str__(self):
        return f"{self.user.username} - {self.role.name}"


class RolePermission(models.Model):
    """Many-to-many relationship between Role and Permission"""
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    granted_at = models.DateTimeField(auto_now_add=True)
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_permissions'
    )

    class Meta:
        db_table = 'auth_role_permission'
        verbose_name = 'Role Permission'
        verbose_name_plural = 'Role Permissions'
        unique_together = [['role', 'permission']]

    def __str__(self):
        return f"{self.role.name} - {self.permission.codename}"
