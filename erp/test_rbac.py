#!/usr/bin/env python
"""
Test script to verify RBAC system functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp.settings')
django.setup()

from authentication.models import User, Role, Permission, UserRole, RolePermission
from authentication.backends import CustomAuthBackend

def test_rbac_system():
    print("=== Testing RBAC System ===\n")
    
    # Test 1: Check if superuser exists
    print("1. Testing superuser...")
    try:
        admin_user = User.objects.get(username='admin')
        print(f"   ✓ Superuser found: {admin_user.username} ({admin_user.email})")
        print(f"   ✓ Is superuser: {admin_user.is_superuser}")
        print(f"   ✓ Is staff: {admin_user.is_staff}")
        print(f"   ✓ Is active: {admin_user.is_active}")
    except User.DoesNotExist:
        print("   ✗ Superuser not found")
        return
    
    # Test 2: Check authentication
    print("\n2. Testing authentication...")
    backend = CustomAuthBackend()
    authenticated_user = backend.authenticate(None, username='admin', password='admin123')
    if authenticated_user:
        print(f"   ✓ Authentication successful for: {authenticated_user.username}")
    else:
        print("   ✗ Authentication failed")
        return
    
    # Test 3: Check roles
    print("\n3. Testing roles...")
    roles = Role.objects.all()
    print(f"   ✓ Total roles created: {roles.count()}")
    for role in roles:
        perm_count = RolePermission.objects.filter(role=role).count()
        print(f"   - {role.name}: {perm_count} permissions")
    
    # Test 4: Check permissions
    print("\n4. Testing permissions...")
    permissions = Permission.objects.all()
    print(f"   ✓ Total permissions created: {permissions.count()}")
    for perm in permissions[:5]:  # Show first 5
        print(f"   - {perm.codename}: {perm.name}")
    if permissions.count() > 5:
        print(f"   ... and {permissions.count() - 5} more")
    
    # Test 5: Create a test user and assign role
    print("\n5. Testing user role assignment...")
    test_user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'is_active': True
        }
    )
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"   ✓ Created test user: {test_user.username}")
    else:
        print(f"   ✓ Test user already exists: {test_user.username}")
    
    # Assign Manager role to test user
    manager_role = Role.objects.get(name='Manager')
    user_role, created = UserRole.objects.get_or_create(
        user=test_user,
        role=manager_role,
        defaults={'assigned_by': admin_user}
    )
    if created:
        print(f"   ✓ Assigned {manager_role.name} role to {test_user.username}")
    else:
        print(f"   ✓ {test_user.username} already has {manager_role.name} role")
    
    # Test 6: Check user permissions
    print("\n6. Testing user permissions...")
    print(f"   Testing permissions for: {test_user.username}")
    
    # Test specific permissions
    test_permissions = ['view_user', 'add_user', 'change_user', 'delete_user']
    for perm in test_permissions:
        has_perm = test_user.has_perm(perm)
        status = "✓" if has_perm else "✗"
        print(f"   {status} {perm}: {has_perm}")
    
    # Test 7: Check superuser permissions
    print("\n7. Testing superuser permissions...")
    print(f"   Testing permissions for: {admin_user.username}")
    for perm in test_permissions:
        has_perm = admin_user.has_perm(perm)
        status = "✓" if has_perm else "✗"
        print(f"   {status} {perm}: {has_perm}")
    
    # Test 8: Database connection
    print("\n8. Testing database connection...")
    try:
        user_count = User.objects.count()
        role_count = Role.objects.count()
        permission_count = Permission.objects.count()
        print(f"   ✓ Database connected successfully")
        print(f"   ✓ Users: {user_count}")
        print(f"   ✓ Roles: {role_count}")
        print(f"   ✓ Permissions: {permission_count}")
    except Exception as e:
        print(f"   ✗ Database error: {e}")
    
    print("\n=== RBAC System Test Complete ===")
    print("✓ All tests passed! Your custom RBAC system is working correctly.")

if __name__ == '__main__':
    test_rbac_system()
